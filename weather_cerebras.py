"""
Модуль для работы с Cerebras Generative API.
Содержит функции для выбора подходящих изображений на основе погодных условий.
"""

import asyncio
import logging
from typing import List, Optional
import aiohttp
import json

from weather_config import (
    CEREBRAS_API_KEY,
    CEREBRAS_BASE_URL,
    CEREBRAS_MODEL,
    CEREBRAS_TEMPERATURE,
    CEREBRAS_TOP_P,
    CEREBRAS_MAX_TOKENS,
    CEREBRAS_STREAM,
    AVAILABLE_IMAGE_NAMES,
    DEFAULT_IMAGE
)

# Настройка логирования
logger = logging.getLogger(__name__)


async def select_image(description: str, available_images: List[str], current_time: str) -> str:
    """
    Выбирает подходящее изображение для погодных условий с помощью Cerebras API.

    Args:
        description (str): Краткое описание погодных условий
                          (например, "пасмурно, температура 15 °C, ветер 3 м/с")
        available_images (List[str]): Список доступных названий файлов изображений
        current_time (str): Текущее время в формате "dd.MM.yyyy HH:MM (MSK)"

    Returns:
        str: Название файла изображения (например, "cloudy_day.png")

    Raises:
        Exception: При ошибках сети или API возвращает изображение по умолчанию
    """
    try:
        # Формируем текст запроса для модели
        prompt = _create_prompt(description, available_images, current_time)

        # Подготавливаем данные для запроса
        request_data = {
            "model": CEREBRAS_MODEL,
            "messages": [
                {
                    "role": "user",
                    "content": prompt
                }
            ],
            "temperature": CEREBRAS_TEMPERATURE,
            "top_p": CEREBRAS_TOP_P,
            "max_tokens": CEREBRAS_MAX_TOKENS,
            "stream": CEREBRAS_STREAM
        }

        # Заголовки для запроса
        headers = {
            "Content-Type": "application/json",
            "Authorization": f"Bearer {CEREBRAS_API_KEY}"
        }

        logger.info(f"Отправляем запрос к Cerebras API для выбора изображения")
        logger.debug(f"Описание погоды: {description}")
        logger.debug(f"Время: {current_time}")

        # Отправляем асинхронный запрос
        async with aiohttp.ClientSession() as session:
            async with session.post(
                CEREBRAS_BASE_URL,
                json=request_data,
                headers=headers,
                timeout=aiohttp.ClientTimeout(total=30)
            ) as response:

                if response.status != 200:
                    logger.error(f"Cerebras API вернул статус {response.status}")
                    response_text = await response.text()
                    logger.error(f"Ответ сервера: {response_text}")
                    return DEFAULT_IMAGE

                # Парсим JSON ответ
                response_data = await response.json()

                # Извлекаем название изображения из ответа
                selected_image = _extract_image_name(response_data, available_images)

                logger.info(f"Cerebras API выбрал изображение: {selected_image}")
                return selected_image

    except asyncio.TimeoutError:
        logger.error("Таймаут при запросе к Cerebras API")
        return DEFAULT_IMAGE
    except aiohttp.ClientError as e:
        logger.error(f"Ошибка сети при запросе к Cerebras API: {e}")
        return DEFAULT_IMAGE
    except json.JSONDecodeError as e:
        logger.error(f"Ошибка парсинга JSON ответа от Cerebras API: {e}")
        return DEFAULT_IMAGE
    except Exception as e:
        logger.error(f"Неожиданная ошибка при работе с Cerebras API: {e}")
        return DEFAULT_IMAGE


def _create_prompt(description: str, available_images: List[str], current_time: str) -> str:
    """
    Создает текст запроса для модели Cerebras.

    Args:
        description (str): Описание погодных условий
        available_images (List[str]): Список доступных изображений
        current_time (str): Текущее время

    Returns:
        str: Сформированный промпт для модели
    """
    # Формируем список изображений для модели
    images_list = ", ".join(available_images)

    prompt = f"""Сейчас {current_time}.
Погодные условия: {description}

Выбери лучшую картинку для этих погодных условий из доступных вариантов.

Доступные изображения: {images_list}

Учитывай время суток при выборе (day/evening/night в названии файла).
Ответь только названием файла, без дополнительных объяснений."""

    return prompt


def _extract_image_name(response_data: dict, available_images: List[str]) -> str:
    """
    Извлекает название изображения из ответа Cerebras API.

    Args:
        response_data (dict): JSON ответ от API
        available_images (List[str]): Список доступных изображений для валидации

    Returns:
        str: Название файла изображения или изображение по умолчанию
    """
    try:
        # Извлекаем текст ответа модели
        content = response_data.get("choices", [{}])[0].get("message", {}).get("content", "")

        if not content:
            logger.warning("Пустой ответ от Cerebras API")
            return DEFAULT_IMAGE

        # Очищаем ответ от лишних символов и пробелов
        selected_image = content.strip().lower()

        # Ищем точное совпадение с доступными изображениями (без учета регистра)
        for image in available_images:
            if image.lower() == selected_image:
                logger.debug(f"Найдено точное совпадение: {image}")
                return image

        # Ищем частичное совпадение (если модель вернула часть названия)
        for image in available_images:
            if selected_image in image.lower() or image.lower() in selected_image:
                logger.debug(f"Найдено частичное совпадение: {image}")
                return image

        logger.warning(f"Модель вернула неизвестное изображение: '{content}'. Используем изображение по умолчанию.")
        return DEFAULT_IMAGE

    except (KeyError, IndexError, TypeError) as e:
        logger.error(f"Ошибка при извлечении названия изображения из ответа API: {e}")
        logger.debug(f"Структура ответа: {response_data}")
        return DEFAULT_IMAGE


async def test_cerebras_connection() -> bool:
    """
    Тестирует подключение к Cerebras API.

    Returns:
        bool: True если подключение успешно, False в противном случае
    """
    test_description = "ясно, температура 20 °C, ветер 2 м/с"
    test_time = "01.01.2025 12:00 (MSK)"
    test_images = ["clear_day.png", "cloudy_day.png"]

    try:
        result = await select_image(test_description, test_images, test_time)
        logger.info(f"Тест Cerebras API успешен. Результат: {result}")
        return True
    except Exception as e:
        logger.error(f"Тест Cerebras API неуспешен: {e}")
        return False


# Функция для синхронного тестирования (если нужно)
def test_cerebras_sync() -> bool:
    """
    Синхронная версия теста подключения к Cerebras API.

    Returns:
        bool: True если подключение успешно, False в противном случае
    """
    return asyncio.run(test_cerebras_connection())
