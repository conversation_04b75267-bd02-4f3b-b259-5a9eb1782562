#!/usr/bin/env python3
"""
Тестовый скрипт для проверки конфигурации Weather Bot.
Запускает валидацию и выводит информацию о настройках.
"""

import sys
import os

# Добавляем текущую папку в путь для импорта модулей
sys.path.insert(0, os.path.dirname(os.path.abspath(__file__)))

try:
    from weather_config import validate_config, print_config_info, AVAILABLE_IMAGES
    
    print("🔧 Тестирование конфигурации Weather Bot...")
    print()
    
    # Выводим информацию о конфигурации
    print_config_info()
    print()
    
    # Проверяем валидность конфигурации
    print("🔍 Проверка конфигурации...")
    if validate_config():
        print("✅ Конфигурация корректна!")
    else:
        print("❌ Обнаружены ошибки в конфигурации!")
        print()
        print("💡 Для исправления:")
        print("1. Создайте файл .env на основе .env.example")
        print("2. Заполните обязательные параметры:")
        print("   - TELEGRAM_TOKEN")
        print("   - OWM_API_KEY") 
        print("   - CHANNEL_ID")
        sys.exit(1)
    
    print()
    print(f"📁 Найдено изображений: {len(AVAILABLE_IMAGES)}")
    
    # Проверяем несколько примеров изображений
    sample_images = list(AVAILABLE_IMAGES.items())[:5]
    print("📸 Примеры изображений:")
    for name, path in sample_images:
        exists = "✅" if os.path.exists(path) else "❌"
        print(f"   {exists} {name} -> {path}")
    
    if len(AVAILABLE_IMAGES) > 5:
        print(f"   ... и ещё {len(AVAILABLE_IMAGES) - 5} изображений")
    
    print()
    print("🎉 Тест конфигурации завершён успешно!")
    
except ImportError as e:
    print(f"❌ Ошибка импорта: {e}")
    print("Убедитесь, что файл weather_config.py существует и корректен.")
    sys.exit(1)
except Exception as e:
    print(f"❌ Неожиданная ошибка: {e}")
    sys.exit(1)
