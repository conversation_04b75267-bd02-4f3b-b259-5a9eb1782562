"""
Модуль для работы с OpenWeatherMap One Call API 3.0.
Содержит функции для получения и парсинга погодных данных.
"""

import asyncio
import logging
from datetime import datetime, timezone
from typing import Dict, List, Optional, Any
from zoneinfo import ZoneInfo

import aiohttp

from weather_config import (
    OWM_API_KEY, OWM_BASE_URL, OWM_EXCLUDE_PARTS,
    OWM_UNITS, OWM_LANG, LAT, LON, TZ
)

# Настройка логирования
logger = logging.getLogger(__name__)

# Русские названия дней недели
WEEKDAYS_RU = [
    'Пн', 'Вт', 'Ср', 'Чт', 'Пт', 'Сб', 'Вс'
]

# Русские названия месяцев
MONTHS_RU = [
    '', 'января', 'февраля', 'марта', 'апреля', 'мая', 'июня',
    'июля', 'августа', 'сентября', 'октября', 'ноября', 'декабря'
]


async def fetch_weather_data(session: aiohttp.ClientSession, exclude: str = None) -> Optional[Dict[str, Any]]:
    """
    Получает данные о погоде от OpenWeatherMap One Call API 3.0.

    Args:
        session: Асинхронная HTTP сессия
        exclude: Части API ответа для исключения (например, 'minutely,alerts')

    Returns:
        Словарь с данными о погоде или None в случае ошибки
    """
    if exclude is None:
        exclude = OWM_EXCLUDE_PARTS

    # Формируем параметры запроса
    params = {
        'lat': LAT,
        'lon': LON,
        'exclude': exclude,
        'units': OWM_UNITS,
        'lang': OWM_LANG,
        'appid': OWM_API_KEY
    }

    try:
        logger.info(f"Запрос погодных данных для координат {LAT}, {LON}")

        async with session.get(OWM_BASE_URL, params=params) as response:
            if response.status == 200:
                data = await response.json()
                logger.info("Погодные данные успешно получены")
                return data
            else:
                logger.error(f"Ошибка API OpenWeatherMap: {response.status} - {await response.text()}")
                return None

    except asyncio.TimeoutError:
        logger.error("Таймаут при запросе к OpenWeatherMap API")
        return None
    except aiohttp.ClientError as e:
        logger.error(f"Ошибка HTTP клиента: {e}")
        return None
    except Exception as e:
        logger.error(f"Неожиданная ошибка при запросе погоды: {e}")
        return None


def parse_current(data: Dict[str, Any]) -> Optional[Dict[str, Any]]:
    """
    Парсит данные текущей погоды.

    Args:
        data: Словарь с данными от OpenWeatherMap API

    Returns:
        Словарь с текущими погодными условиями или None в случае ошибки
    """
    try:
        current = data.get('current', {})

        if not current:
            logger.error("Отсутствуют данные текущей погоды")
            return None

        # Извлекаем основные данные
        weather_info = current.get('weather', [{}])[0]

        result = {
            'description': weather_info.get('description', 'Неизвестно'),
            'temp': round(current.get('temp', 0)),
            'feels_like': round(current.get('feels_like', 0)),
            'wind_speed': round(current.get('wind_speed', 0), 1),
            'wind_deg': current.get('wind_deg', 0),
            'humidity': current.get('humidity', 0),
            'pressure': current.get('pressure', 0),
            'visibility': current.get('visibility', 0),
            'uvi': current.get('uvi', 0),
            'clouds': current.get('clouds', 0),
            'dt': current.get('dt', 0)
        }

        logger.info(f"Текущая погода: {result['temp']}°C, {result['description']}")
        return result

    except Exception as e:
        logger.error(f"Ошибка при парсинге текущей погоды: {e}")
        return None


def parse_today(data: Dict[str, Any]) -> Optional[Dict[str, Any]]:
    """
    Парсит данные погоды на сегодня по временам суток.

    Args:
        data: Словарь с данными от OpenWeatherMap API

    Returns:
        Словарь с погодой по временам суток или None в случае ошибки
    """
    try:
        daily = data.get('daily', [])

        if not daily:
            logger.error("Отсутствуют данные прогноза на день")
            return None

        today = daily[0]  # Первый день - сегодня

        # Извлекаем температуры по временам суток
        temp_data = today.get('temp', {})
        feels_like_data = today.get('feels_like', {})

        # Получаем описание погоды
        weather_info = today.get('weather', [{}])[0]

        result = {
            'morn': {
                'temp': round(temp_data.get('morn', 0)),
                'feels_like': round(feels_like_data.get('morn', 0)),
                'description': weather_info.get('description', 'Неизвестно')
            },
            'day': {
                'temp': round(temp_data.get('day', 0)),
                'feels_like': round(feels_like_data.get('day', 0)),
                'description': weather_info.get('description', 'Неизвестно')
            },
            'eve': {
                'temp': round(temp_data.get('eve', 0)),
                'feels_like': round(feels_like_data.get('eve', 0)),
                'description': weather_info.get('description', 'Неизвестно')
            },
            'night': {
                'temp': round(temp_data.get('night', 0)),
                'feels_like': round(feels_like_data.get('night', 0)),
                'description': weather_info.get('description', 'Неизвестно')
            },
            'wind_speed': round(today.get('wind_speed', 0), 1),
            'wind_deg': today.get('wind_deg', 0),
            'humidity': today.get('humidity', 0),
            'pressure': today.get('pressure', 0),
            'sunrise': today.get('sunrise', 0),
            'sunset': today.get('sunset', 0),
            'dt': today.get('dt', 0)
        }

        logger.info(f"Погода на сегодня: утром {result['morn']['temp']}°C, днем {result['day']['temp']}°C")
        return result

    except Exception as e:
        logger.error(f"Ошибка при парсинге погоды на сегодня: {e}")
        return None


def parse_week(data: Dict[str, Any]) -> Optional[List[Dict[str, Any]]]:
    """
    Парсит данные прогноза погоды на неделю.

    Args:
        data: Словарь с данными от OpenWeatherMap API

    Returns:
        Список словарей с прогнозом на 7 дней или None в случае ошибки
    """
    try:
        daily = data.get('daily', [])

        if len(daily) < 7:
            logger.error(f"Недостаточно данных для недельного прогноза: {len(daily)} дней")
            return None

        result = []

        for i, day_data in enumerate(daily[:7]):  # Берем только первые 7 дней
            # Извлекаем температуры
            temp_data = day_data.get('temp', {})
            feels_like_data = day_data.get('feels_like', {})

            # Получаем описание погоды
            weather_info = day_data.get('weather', [{}])[0]

            # Конвертируем timestamp в дату
            dt = day_data.get('dt', 0)
            date_obj = datetime.fromtimestamp(dt, tz=ZoneInfo(TZ))

            day_info = {
                'date': date_obj.strftime('%d.%m'),  # Формат: 30.07
                'weekday': WEEKDAYS_RU[date_obj.weekday()],  # Пн, Вт, и т.д.
                'day_name': f"{WEEKDAYS_RU[date_obj.weekday()]}, {date_obj.day} {MONTHS_RU[date_obj.month]}",
                'temp_min': round(temp_data.get('night', temp_data.get('min', 0))),
                'temp_max': round(temp_data.get('day', temp_data.get('max', 0))),
                'feels_like_day': round(feels_like_data.get('day', 0)),
                'description': weather_info.get('description', 'Неизвестно'),
                'wind_speed': round(day_data.get('wind_speed', 0), 1),
                'wind_deg': day_data.get('wind_deg', 0),
                'humidity': day_data.get('humidity', 0),
                'pressure': day_data.get('pressure', 0),
                'clouds': day_data.get('clouds', 0),
                'dt': dt
            }

            result.append(day_info)

        logger.info(f"Прогноз на неделю: {len(result)} дней")
        return result

    except Exception as e:
        logger.error(f"Ошибка при парсинге недельного прогноза: {e}")
        return None


def format_sun_times(sunrise_ts: int, sunset_ts: int, tz: str = TZ) -> Dict[str, str]:
    """
    Конвертирует UNIX-время восхода и заката в строки по заданному часовому поясу.

    Args:
        sunrise_ts: UNIX timestamp восхода солнца
        sunset_ts: UNIX timestamp заката солнца
        tz: Часовой пояс (по умолчанию из конфигурации)

    Returns:
        Словарь с отформатированными временами восхода и заката
    """
    try:
        timezone_info = ZoneInfo(tz)

        # Конвертируем timestamps в datetime объекты
        sunrise_dt = datetime.fromtimestamp(sunrise_ts, tz=timezone_info)
        sunset_dt = datetime.fromtimestamp(sunset_ts, tz=timezone_info)

        # Проверяем, не прошел ли уже восход сегодня
        now = datetime.now(timezone_info)

        # Если восход уже прошел, показываем восход на следующий день
        if now.time() > sunrise_dt.time():
            # Добавляем один день к восходу
            from datetime import timedelta
            next_sunrise_dt = sunrise_dt + timedelta(days=1)
            sunrise_str = f"завтра в {next_sunrise_dt.strftime('%H:%M')}"
        else:
            sunrise_str = sunrise_dt.strftime('%H:%M')

        sunset_str = sunset_dt.strftime('%H:%M')

        result = {
            'sunrise': sunrise_str,
            'sunset': sunset_str,
            'sunrise_raw': sunrise_dt.strftime('%H:%M'),
            'sunset_raw': sunset_dt.strftime('%H:%M'),
            'is_sunrise_tomorrow': now.time() > sunrise_dt.time()
        }

        logger.debug(f"Время восхода/заката: {sunrise_str} / {sunset_str}")
        return result

    except Exception as e:
        logger.error(f"Ошибка при форматировании времени восхода/заката: {e}")
        return {
            'sunrise': 'Неизвестно',
            'sunset': 'Неизвестно',
            'sunrise_raw': 'Неизвестно',
            'sunset_raw': 'Неизвестно',
            'is_sunrise_tomorrow': False
        }


def get_wind_direction(degrees: int) -> str:
    """
    Конвертирует градусы направления ветра в текстовое описание.

    Args:
        degrees: Направление ветра в градусах (0-360)

    Returns:
        Текстовое описание направления ветра
    """
    directions = [
        'С', 'ССВ', 'СВ', 'ВСВ', 'В', 'ВЮВ', 'ЮВ', 'ЮЮВ',
        'Ю', 'ЮЮЗ', 'ЮЗ', 'ЗЮЗ', 'З', 'ЗСЗ', 'СЗ', 'ССЗ'
    ]

    # Нормализуем градусы к диапазону 0-360
    degrees = degrees % 360

    # Каждое направление занимает 22.5 градуса
    index = round(degrees / 22.5) % 16

    return directions[index]


def get_current_time_msk() -> str:
    """
    Возвращает текущее время в формате для Cerebras API.

    Returns:
        Строка с текущим временем в формате "dd.MM.yyyy HH:MM (MSK)"
    """
    try:
        msk_tz = ZoneInfo(TZ)
        now = datetime.now(msk_tz)
        return now.strftime('%d.%m.%Y %H:%M (MSK)')
    except Exception as e:
        logger.error(f"Ошибка при получении текущего времени: {e}")
        return datetime.now().strftime('%d.%m.%Y %H:%M (MSK)')


# Вспомогательные функции для тестирования
async def test_api_connection() -> bool:
    """
    Тестирует подключение к OpenWeatherMap API.

    Returns:
        True если API доступен, False в противном случае
    """
    try:
        async with aiohttp.ClientSession() as session:
            data = await fetch_weather_data(session, exclude='minutely,alerts,hourly')
            return data is not None
    except Exception as e:
        logger.error(f"Ошибка при тестировании API: {e}")
        return False


if __name__ == "__main__":
    # Простой тест модуля
    async def main():
        print("Тестирование модуля weather_api...")

        # Тест подключения к API
        if await test_api_connection():
            print("✅ Подключение к OpenWeatherMap API успешно")

            # Получаем и парсим данные
            async with aiohttp.ClientSession() as session:
                data = await fetch_weather_data(session)

                if data:
                    print("✅ Данные получены")

                    # Тестируем парсинг
                    current = parse_current(data)
                    if current:
                        print(f"✅ Текущая погода: {current['temp']}°C, {current['description']}")

                    today = parse_today(data)
                    if today:
                        print(f"✅ Погода на сегодня: утром {today['morn']['temp']}°C")

                    week = parse_week(data)
                    if week:
                        print(f"✅ Прогноз на неделю: {len(week)} дней")

                    # Тест времени восхода/заката
                    if today:
                        sun_times = format_sun_times(today['sunrise'], today['sunset'])
                        print(f"✅ Восход: {sun_times['sunrise']}, Закат: {sun_times['sunset']}")
                else:
                    print("❌ Не удалось получить данные")
        else:
            print("❌ Не удалось подключиться к OpenWeatherMap API")

    # Запускаем тест
    asyncio.run(main())
