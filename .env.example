# Пример файла с переменными окружения для Weather Bot
# Скопируйте этот файл в .env и заполните своими значениями

# =============================================================================
# ОБЯЗАТЕЛЬНЫЕ ПАРАМЕТРЫ
# =============================================================================

# Токен Telegram бота (получить у @BotFather)
TELEGRAM_TOKEN=your_telegram_bot_token_here

# API ключ OpenWeatherMap (получить на https://openweathermap.org/api)
OWM_API_KEY=your_openweathermap_api_key_here

# ID канала для публикации прогнозов (например: @your_channel или -1001234567890)
CHANNEL_ID=@your_channel_username

# =============================================================================
# ОПЦИОНАЛЬНЫЕ ПАРАМЕТРЫ (уже имеют значения по умолчанию)
# =============================================================================

# Cerebras API ключ (уже задан в коде)
# CEREBRAS_API_KEY=csk-cnjd9w9nx5kjtpj99936fexmhpxdc586ny3k6rc96rfn4rkw

# Координаты местности (по умолчанию - Гагаринский район Севастополя)
# LAT=44.600
# LON=33.517

# Часовой пояс (по умолчанию - Московское время)
# TZ=Europe/Moscow

# Интервалы обновления в секундах
# UPDATE_INTERVAL_WEEK=43200    # 12 часов
# UPDATE_INTERVAL_TODAY=3600    # 1 час  
# UPDATE_INTERVAL_CURRENT=600   # 10 минут

# Ключевое слово для активации бота
# TRIGGER_KEYWORD=weather

# Файл для хранения состояния
# STATE_FILE=weather_state.json

# Папка с изображениями
# IMAGES_DIR=images
