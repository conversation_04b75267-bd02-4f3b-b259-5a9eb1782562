"""
Конфигурационный модуль с константами и настройками.
Содержит токены, API-ключи, координаты и интервалы обновления.
"""

import os
from typing import Dict, List

# Загружаем переменные окружения из .env файла (если он существует)
try:
    from dotenv import load_dotenv
    load_dotenv()
except ImportError:
    # Если python-dotenv не установлен, продолжаем без него
    pass

# =============================================================================
# API КЛЮЧИ И ТОКЕНЫ
# =============================================================================

# Telegram Bot Token - получить у @BotFather
TELEGRAM_TOKEN = os.getenv('TELEGRAM_TOKEN', '')

# OpenWeatherMap API Key - получить на https://openweathermap.org/api
OWM_API_KEY = os.getenv('OWM_API_KEY', '')

# Cerebras API Key - указан в техническом задании
CEREBRAS_API_KEY = os.getenv('CEREBRAS_API_KEY', 'csk-cnjd9w9nx5kjtpj99936fexmhpxdc586ny3k6rc96rfn4rkw')

# =============================================================================
# ГЕОГРАФИЧЕСКИЕ КООРДИНАТЫ
# =============================================================================

# Координаты Гагаринского района Севастополя
# Источник: https://en.wikipedia.org/wiki/Gagarinsky_District,_Sevastopol
LAT = float(os.getenv('LAT', '44.600'))  # Широта
LON = float(os.getenv('LON', '33.517'))  # Долгота

# Часовой пояс для отображения времени восхода/заката
TZ = os.getenv('TZ', 'Europe/Moscow')

# =============================================================================
# ИНТЕРВАЛЫ ОБНОВЛЕНИЯ (в секундах)
# =============================================================================

# Прогноз на неделю - каждые 12 часов
UPDATE_INTERVAL_WEEK = int(os.getenv('UPDATE_INTERVAL_WEEK', '43200'))

# Прогноз на сегодня - каждый час
UPDATE_INTERVAL_TODAY = int(os.getenv('UPDATE_INTERVAL_TODAY', '3600'))

# Погода сейчас - каждые 10 минут
UPDATE_INTERVAL_CURRENT = int(os.getenv('UPDATE_INTERVAL_CURRENT', '600'))

# =============================================================================
# TELEGRAM НАСТРОЙКИ
# =============================================================================

# ID канала для публикации прогнозов (можно использовать @channelusername или числовой ID)
CHANNEL_ID = os.getenv('CHANNEL_ID', '')

# Ключевое слово для активации бота
TRIGGER_KEYWORD = os.getenv('TRIGGER_KEYWORD', 'weather').lower()

# =============================================================================
# ДОСТУПНЫЕ ИЗОБРАЖЕНИЯ
# =============================================================================

# Словарь доступных изображений с путями к файлам
# Ключ - название для модели Cerebras, значение - путь к файлу
AVAILABLE_IMAGES: Dict[str, str] = {
    # Ясная погода
    'clear_day.png': 'images/clear_day.png',
    'clear_evening.png': 'images/clear_evening.png',
    'clear_night.png': 'images/clear_night.png',

    # Малооблачно
    'few_clouds_day.png': 'images/few_clouds_day.png',
    'few_clouds_evening.png': 'images/few_clouds_evening.png',
    'few_clouds_night.png': 'images/few_clouds_night.png',

    # Облачно
    'cloudy_day.png': 'images/cloudy_day.png',
    'cloudy_evening.png': 'images/cloudy_evening.png',
    'cloudy_night.png': 'images/cloudy_night.png',

    # Пасмурно
    'overcast_day.png': 'images/overcast_day.png',
    'overcast_evening.png': 'images/overcast_evening.png',
    'overcast_night.png': 'images/overcast_night.png',

    # Дождь
    'rain_day.png': 'images/rain_day.png',
    'rain_evening.png': 'images/rain_evening.png',
    'rain_night.png': 'images/rain_night.png',

    # Сильный дождь
    'heavy_rain_day.png': 'images/heavy_rain_day.png',
    'heavy_rain_evening.png': 'images/heavy_rain_evening.png',
    'heavy_rain_night.png': 'images/heavy_rain_night.png',

    # Снег
    'snow_day.png': 'images/snow_day.png',
    'snow_evening.png': 'images/snow_evening.png',
    'snow_night.png': 'images/snow_night.png',

    # Гроза
    'thunderstorm_day.png': 'images/thunderstorm_day.png',
    'thunderstorm_evening.png': 'images/thunderstorm_evening.png',
    'thunderstorm_night.png': 'images/thunderstorm_night.png',

    # Туман
    'fog_day.png': 'images/fog_day.png',
    'fog_evening.png': 'images/fog_evening.png',
    'fog_night.png': 'images/fog_night.png',

    # Дымка
    'haze_day.png': 'images/haze_day.png',
    'haze_evening.png': 'images/haze_evening.png',
    'haze_night.png': 'images/haze_night.png',

    # Ветрено
    'windy_day.png': 'images/windy_day.png',
    'windy_evening.png': 'images/windy_evening.png',
    'windy_night.png': 'images/windy_night.png',

    # Ледяной дождь
    'freezing_rain_day.png': 'images/freezing_rain_day.png',
    'freezing_rain_evening.png': 'images/freezing_rain_evening.png',
    'freezing_rain_night.png': 'images/freezing_rain_night.png',

    # Град
    'hail_day.png': 'images/hail_day.png',
    'hail_evening.png': 'images/hail_evening.png',
    'hail_night.png': 'images/hail_night.png',

    # Метель
    'blizzard_day.png': 'images/blizzard_day.png',
    'blizzard_evening.png': 'images/blizzard_evening.png',
    'blizzard_night.png': 'images/blizzard_night.png',

    # Заморозки
    'frost_day.png': 'images/frost_day.png',
    'frost_evening.png': 'images/frost_evening.png',
    'frost_night.png': 'images/frost_night.png',
}

# Список названий изображений для передачи в Cerebras API
AVAILABLE_IMAGE_NAMES: List[str] = list(AVAILABLE_IMAGES.keys())

# Изображение по умолчанию (если модель вернет неизвестное название)
DEFAULT_IMAGE = 'cloudy_day.png'

# =============================================================================
# OPENWEATHERMAP API НАСТРОЙКИ
# =============================================================================

# Базовый URL для OpenWeatherMap One Call API 3.0
OWM_BASE_URL = 'https://api.openweathermap.org/data/3.0/onecall'

# Части API ответа, которые нужно исключить для экономии трафика
OWM_EXCLUDE_PARTS = 'minutely,alerts'

# Единицы измерения (metric для Цельсия)
OWM_UNITS = 'metric'

# Язык ответа
OWM_LANG = 'ru'

# =============================================================================
# CEREBRAS API НАСТРОЙКИ
# =============================================================================

# Базовый URL для Cerebras API
CEREBRAS_BASE_URL = 'https://api.cerebras.ai/v1/chat/completions'

# Модель для использования
CEREBRAS_MODEL = 'qwen-3-235b-a22b-instruct-2507'

# Параметры генерации
CEREBRAS_TEMPERATURE = 0  # Детерминированный выбор
CEREBRAS_TOP_P = 1
CEREBRAS_MAX_TOKENS = 50  # Достаточно для названия файла
CEREBRAS_STREAM = False

# =============================================================================
# ФАЙЛЫ И ПУТИ
# =============================================================================

# Файл для хранения состояния бота (message_id и временные метки)
STATE_FILE = os.getenv('STATE_FILE', 'weather_state.json')

# Папка с изображениями
IMAGES_DIR = os.getenv('IMAGES_DIR', 'images')

# =============================================================================
# ВАЛИДАЦИЯ КОНФИГУРАЦИИ
# =============================================================================

def validate_config() -> bool:
    """
    Проверяет корректность конфигурации.
    Возвращает True, если все обязательные параметры заданы.
    """
    errors = []

    if not TELEGRAM_TOKEN:
        errors.append("TELEGRAM_TOKEN не задан")

    if not OWM_API_KEY:
        errors.append("OWM_API_KEY не задан")

    if not CHANNEL_ID:
        errors.append("CHANNEL_ID не задан")

    # Проверяем существование файлов изображений
    import os
    missing_images = []
    for name, path in AVAILABLE_IMAGES.items():
        if not os.path.exists(path):
            missing_images.append(f"{name} -> {path}")

    if missing_images:
        errors.append(f"Отсутствуют файлы изображений: {', '.join(missing_images)}")

    if errors:
        print("Ошибки конфигурации:")
        for error in errors:
            print(f"  - {error}")
        return False

    return True

# =============================================================================
# ИНФОРМАЦИЯ О КОНФИГУРАЦИИ
# =============================================================================

def print_config_info():
    """Выводит информацию о текущей конфигурации (без секретных данных)."""
    print("=== КОНФИГУРАЦИЯ WEATHER BOT ===")
    print(f"Координаты: {LAT}, {LON}")
    print(f"Часовой пояс: {TZ}")
    print(f"Интервалы обновления:")
    print(f"  - Неделя: {UPDATE_INTERVAL_WEEK} сек ({UPDATE_INTERVAL_WEEK//3600} ч)")
    print(f"  - Сегодня: {UPDATE_INTERVAL_TODAY} сек ({UPDATE_INTERVAL_TODAY//60} мин)")
    print(f"  - Сейчас: {UPDATE_INTERVAL_CURRENT} сек ({UPDATE_INTERVAL_CURRENT//60} мин)")
    print(f"Доступно изображений: {len(AVAILABLE_IMAGES)}")
    print(f"Ключевое слово: '{TRIGGER_KEYWORD}'")
    print(f"Файл состояния: {STATE_FILE}")
    print("================================")
