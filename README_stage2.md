# Weather Bot - Этап 2: Настройка конфигурации

## ✅ Выполненные задачи

### 1. Создан файл `weather_config.py`

Файл содержит все необходимые константы и настройки:

#### API ключи и токены:
- `TELEGRAM_TOKEN` - токен Telegram бота
- `OWM_API_KEY` - ключ OpenWeatherMap API
- `CEREBRAS_API_KEY` - ключ Cerebras API (уже задан согласно ТЗ)

#### Географические координаты:
- `LAT = 44.600` - широта Гагаринского района Севастополя
- `LON = 33.517` - долгота Гагаринского района Севастополя  
- `TZ = 'Europe/Moscow'` - часовой пояс для отображения времени

#### Интервалы обновления:
- `UPDATE_INTERVAL_WEEK = 43200` сек (12 часов) - прогноз на неделю
- `UPDATE_INTERVAL_TODAY = 3600` сек (1 час) - прогноз на сегодня
- `UPDATE_INTERVAL_CURRENT = 600` сек (10 минут) - текущая погода

#### Настройки изображений:
- `AVAILABLE_IMAGES` - словарь с 45 изображениями для разных погодных условий
- Поддержка времени суток (день/вечер/ночь) для каждого типа погоды
- `DEFAULT_IMAGE` - изображение по умолчанию

### 2. Добавлена поддержка переменных окружения

Все настройки можно переопределить через переменные окружения:
- Использование `os.getenv()` с значениями по умолчанию
- Безопасное хранение токенов и ключей вне исходного кода
- Гибкость конфигурации для разных сред (разработка/продакшн)

### 3. Создан файл `.env.example`

Шаблон для настройки переменных окружения с:
- Описанием всех доступных параметров
- Разделением на обязательные и опциональные
- Инструкциями по получению API ключей

### 4. Добавлена валидация конфигурации

Функция `validate_config()` проверяет:
- Наличие обязательных параметров
- Существование файлов изображений
- Корректность настроек

### 5. Создан тестовый скрипт `test_config.py`

Позволяет:
- Проверить корректность конфигурации
- Вывести информацию о настройках
- Найти отсутствующие параметры или файлы

## 📁 Структура изображений

В папке `images/` находится 45 изображений для различных погодных условий:

### Типы погоды:
- **clear** - ясная погода
- **few_clouds** - малооблачно  
- **cloudy** - облачно
- **overcast** - пасмурно
- **rain** - дождь
- **heavy_rain** - сильный дождь
- **snow** - снег
- **thunderstorm** - гроза
- **fog** - туман
- **haze** - дымка
- **windy** - ветрено
- **freezing_rain** - ледяной дождь
- **hail** - град
- **blizzard** - метель
- **frost** - заморозки

### Время суток:
Каждый тип погоды имеет варианты для:
- `_day.png` - день
- `_evening.png` - вечер  
- `_night.png` - ночь

## 🔧 Использование

### Базовая настройка:
1. Скопируйте `.env.example` в `.env`
2. Заполните обязательные параметры:
   ```
   TELEGRAM_TOKEN=your_bot_token
   OWM_API_KEY=your_openweathermap_key
   CHANNEL_ID=@your_channel
   ```

### Проверка конфигурации:
```bash
python test_config.py
```

### Импорт в других модулях:
```python
from weather_config import (
    TELEGRAM_TOKEN, OWM_API_KEY, CEREBRAS_API_KEY,
    LAT, LON, TZ,
    UPDATE_INTERVAL_WEEK, UPDATE_INTERVAL_TODAY, UPDATE_INTERVAL_CURRENT,
    AVAILABLE_IMAGES, DEFAULT_IMAGE,
    validate_config, print_config_info
)
```

## 🎯 Следующие этапы

Этап 2 завершён успешно. Конфигурация готова для использования в следующих этапах:

- **Этап 3**: Реализация модуля работы с OpenWeatherMap API
- **Этап 4**: Реализация обращения к Cerebras API
- **Этап 5**: Разработка утилит форматирования

## 🔑 Настроенные API ключи

Конфигурация полностью настроена с рабочими API ключами:

- ✅ **Telegram Bot Token**: `**********************************************`
- ✅ **OpenWeatherMap API Key**: `********************************`
- ✅ **Channel ID**: `2769056078`
- ✅ **Cerebras API Key**: `csk-cnjd9w9nx5kjtpj99936fexmhpxdc586ny3k6rc96rfn4rkw` (из ТЗ)

Все ключи сохранены в файле `.env` и автоматически загружаются при импорте модуля.

## 📋 Проверочный список

- ✅ Создан `weather_config.py` со всеми константами
- ✅ Добавлена поддержка переменных окружения с `python-dotenv`
- ✅ Настроен список из 45 изображений
- ✅ Добавлена валидация конфигурации
- ✅ Создан `.env.example` с инструкциями
- ✅ Создан рабочий `.env` файл с API ключами
- ✅ Создан тестовый скрипт
- ✅ Проверена корректность импорта модуля
- ✅ Протестирована загрузка всех параметров
- ✅ Подтверждено существование всех 45 изображений
